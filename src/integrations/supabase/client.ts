// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ialayqjpqvnfucximdfb.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlhbGF5cWpwcXZuZnVjeGltZGZiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDk0ODMsImV4cCI6MjA2MjkyNTQ4M30.0PjKqhU-B9VzQ-eEXexMs5mw1YS_ev_o_Tzs6Jxv3sA";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);